package utils

import (
	"encoding/json"
	"fmt"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
	"github.com/tidwall/gjson"
)

// ProductExtractor 转发服务专用的产品信息提取器
// 整合了 types.ProductExtractor 的核心功能和转发服务特定的需求
// 消除了 forward/service.go 中的重复代码
type ProductExtractor struct {
	// 核心产品提取器
	coreExtractor *types.ProductExtractor
}

// NewProductExtractor 创建新的转发产品信息提取器
func NewProductExtractor() *ProductExtractor {
	return &ProductExtractor{
		coreExtractor: types.NewProductExtractor(),
	}
}

// ExtractFromMessage 从Discord消息中提取产品信息列表
// 这是主要的提取方法，整合了多种提取策略
func (fpe *ProductExtractor) ExtractFromMessage(message *discordgo.Message) ([]*types.ProductItem, error) {
	var allProductItems []*types.ProductItem

	// 如果没有embeds，尝试从消息内容提取基础信息
	if len(message.Embeds) == 0 {
		if message.Content != "" {
			product := &types.ProductItem{
				Title:        message.Content,
				Metadata:     make(map[string]interface{}),
				Stock:        0,
				Availability: "unknown",
			}
			allProductItems = append(allProductItems, product)
		}
		return allProductItems, nil
	}

	// 处理每个embed
	for _, embed := range message.Embeds {
		embedData := fpe.ConvertEmbedToMap(embed)
		extractedData := fpe.ExtractFromEmbedData(embedData)
		productItem := fpe.CreateProductFromExtractedData(extractedData)
		if productItem != nil {
			allProductItems = append(allProductItems, productItem)
		}
	}

	return allProductItems, nil
}

// ExtractFromEmbedData 统一的embed数据提取方法（使用gjson）
// 将embed数据提取为标准化的map，键名与ProductItem字段名一致
// 这个方法整合了原 forward/service.go 中的 extractEmbedDataForMapping 逻辑
func (fpe *ProductExtractor) ExtractFromEmbedData(embedData map[string]interface{}) map[string]interface{} {
	extractedData := make(map[string]interface{})

	// 将embedData转换为JSON字符串，以便使用gjson
	jsonData, err := json.Marshal(embedData)
	if err != nil {
		logger.Error("转换embed数据为JSON失败", "error", err)
		return extractedData
	}
	jsonStr := string(jsonData)

	// 使用gjson提取基础字段，键名与ProductItem字段名一致
	if title := gjson.Get(jsonStr, "title").String(); title != "" {
		extractedData["Title"] = title
	}

	if description := gjson.Get(jsonStr, "description").String(); description != "" {
		extractedData["Description"] = description
	}

	if url := gjson.Get(jsonStr, "url").String(); url != "" {
		extractedData["URL"] = url
	}

	if color := gjson.Get(jsonStr, "color"); color.Exists() {
		extractedData["Color"] = color.Int()
	}

	// 提取图片信息
	if imageURL := gjson.Get(jsonStr, "image.url").String(); imageURL != "" {
		extractedData["ImageURL"] = imageURL
	}

	if thumbnailURL := gjson.Get(jsonStr, "thumbnail.url").String(); thumbnailURL != "" {
		extractedData["ThumbnailURL"] = thumbnailURL
	}

	// 提取作者信息
	if authorName := gjson.Get(jsonStr, "author.name").String(); authorName != "" {
		extractedData["AuthorName"] = authorName
	}

	if authorURL := gjson.Get(jsonStr, "author.url").String(); authorURL != "" {
		extractedData["AuthorURL"] = authorURL
	}

	if authorIconURL := gjson.Get(jsonStr, "author.icon_url").String(); authorIconURL != "" {
		extractedData["AuthorIconURL"] = authorIconURL
	}

	// 提取页脚信息
	if footerText := gjson.Get(jsonStr, "footer.text").String(); footerText != "" {
		extractedData["FooterText"] = footerText
	}

	if footerIconURL := gjson.Get(jsonStr, "footer.icon_url").String(); footerIconURL != "" {
		extractedData["FooterIconURL"] = footerIconURL
	}

	// 提取时间戳
	if timestamp := gjson.Get(jsonStr, "timestamp").String(); timestamp != "" {
		extractedData["Timestamp"] = timestamp
	}

	// 使用gjson处理fields字段，将其展开为键值对（保持原始字段名，合并重复字段）
	fieldsResult := gjson.Get(jsonStr, "fields")
	if fieldsResult.Exists() && fieldsResult.IsArray() {
		logger.Info("开始处理fields字段", "fields_count", len(fieldsResult.Array()))

		// 用于跟踪重复字段
		fieldCounts := make(map[string]int)

		fieldsResult.ForEach(func(key, value gjson.Result) bool {
			name := value.Get("name").String()
			fieldValue := value.Get("value").String()

			if name != "" && fieldValue != "" {
				// 检查是否已存在相同字段名
				if existingValue, exists := extractedData[name]; exists {
					// 字段已存在，合并值
					fieldCounts[name]++

					// 将现有值转换为字符串
					existingStr := fmt.Sprintf("%v", existingValue)

					// 合并值，使用分隔符
					mergedValue := existingStr + " | " + fieldValue
					extractedData[name] = mergedValue

					logger.Info("字段合并成功",
						"field_name", name,
						"existing_value", existingStr,
						"new_value", fieldValue,
						"merged_value", mergedValue,
						"occurrence_count", fieldCounts[name]+1)
				} else {
					// 首次出现的字段
					extractedData[name] = fieldValue
					fieldCounts[name] = 0

					logger.Info("字段提取成功",
						"field_name", name,
						"value", fieldValue)
				}
			}

			return true // 继续遍历
		})

		// 记录重复字段统计
		duplicateCount := 0
		for fieldName, count := range fieldCounts {
			if count > 0 {
				duplicateCount++
				logger.Info("发现重复字段",
					"field_name", fieldName,
					"total_occurrences", count+1)
			}
		}

		if duplicateCount > 0 {
			logger.Info("字段合并统计",
				"total_fields", len(fieldCounts),
				"duplicate_fields", duplicateCount)
		}
	}

	logger.Info("embed数据提取完成", "extracted_fields", len(extractedData))
	return extractedData
}

// CreateProductFromExtractedData 从提取的数据创建ProductItem（fallback方法）
// 这个方法整合了原 forward/service.go 中的 createProductFromExtractedData 逻辑
func (fpe *ProductExtractor) CreateProductFromExtractedData(extractedData map[string]interface{}) *types.ProductItem {
	product := &types.ProductItem{
		Metadata:     make(map[string]interface{}),
		Stock:        0,
		Availability: "unknown",
	}

	// 从提取的数据中设置基础字段
	if title, ok := extractedData["Title"].(string); ok && title != "" {
		product.Title = title
	}

	if url, ok := extractedData["URL"].(string); ok && url != "" {
		product.URL = url
	}

	if productID, ok := extractedData["ProductID"].(string); ok && productID != "" {
		product.ProductID = productID
	}

	if price, ok := extractedData["Price"].(string); ok && price != "" {
		product.Price = price
	}

	// 设置其他字段
	if description, ok := extractedData["Description"].(string); ok && description != "" {
		product.Description = description
	}

	if color, ok := extractedData["Color"].(int64); ok {
		product.Color = int(color)
	}

	if imageURL, ok := extractedData["ImageURL"].(string); ok && imageURL != "" {
		product.ImageURL = imageURL
	}

	if thumbnailURL, ok := extractedData["ThumbnailURL"].(string); ok && thumbnailURL != "" {
		product.ThumbnailURL = thumbnailURL
	}

	if authorName, ok := extractedData["AuthorName"].(string); ok && authorName != "" {
		product.AuthorName = authorName
	}

	if authorURL, ok := extractedData["AuthorURL"].(string); ok && authorURL != "" {
		product.AuthorURL = authorURL
	}

	if authorIconURL, ok := extractedData["AuthorIconURL"].(string); ok && authorIconURL != "" {
		product.AuthorIconURL = authorIconURL
	}

	if footerText, ok := extractedData["FooterText"].(string); ok && footerText != "" {
		product.FooterText = footerText
	}

	if footerIconURL, ok := extractedData["FooterIconURL"].(string); ok && footerIconURL != "" {
		product.FooterIconURL = footerIconURL
	}

	if timestamp, ok := extractedData["Timestamp"].(string); ok && timestamp != "" {
		product.Timestamp = timestamp
	}

	// 将其他字段存储到元数据中
	for key, value := range extractedData {
		// 跳过已经映射的标准字段
		if !fpe.isStandardField(key) {
			product.Metadata[key] = value
		}
	}

	// 如果没有基本信息，返回nil
	if product.Title == "" && product.URL == "" && product.ProductID == "" {
		return nil
	}

	return product
}

// ConvertDiscordMessageToMap 将Discord消息转换为map格式
// 这个方法整合了原 forward/service.go 中的 convertDiscordMessageToMap 逻辑
func (fpe *ProductExtractor) ConvertDiscordMessageToMap(message *discordgo.Message) map[string]interface{} {
	messageMap := map[string]interface{}{
		"content":    message.Content,
		"author_id":  message.Author.ID,
		"author":     message.Author.Username,
		"channel_id": message.ChannelID,
		"guild_id":   message.GuildID,
		"message_id": message.ID,
		"timestamp":  message.Timestamp,
	}

	// 转换embeds
	if len(message.Embeds) > 0 {
		embeds := make([]map[string]interface{}, len(message.Embeds))
		for i, embed := range message.Embeds {
			embeds[i] = fpe.ConvertEmbedToMap(embed)
		}
		messageMap["embeds"] = embeds
	}

	return messageMap
}

// ConvertEmbedToMap 将单个Discord embed转换为map格式
// 这个方法整合了原 forward/service.go 中的 convertEmbedToMap 逻辑
func (fpe *ProductExtractor) ConvertEmbedToMap(embed *discordgo.MessageEmbed) map[string]interface{} {
	embedMap := map[string]interface{}{
		"title":       embed.Title,
		"description": embed.Description,
		"url":         embed.URL,
		"color":       embed.Color,
	}

	// 添加图片信息
	if embed.Image != nil {
		embedMap["image"] = map[string]interface{}{
			"url": embed.Image.URL,
		}
	}

	// 添加缩略图信息
	if embed.Thumbnail != nil {
		embedMap["thumbnail"] = map[string]interface{}{
			"url": embed.Thumbnail.URL,
		}
	}

	// 添加作者信息
	if embed.Author != nil {
		authorMap := make(map[string]interface{})
		if embed.Author.Name != "" {
			authorMap["name"] = embed.Author.Name
		}
		if embed.Author.URL != "" {
			authorMap["url"] = embed.Author.URL
		}
		if embed.Author.IconURL != "" {
			authorMap["icon_url"] = embed.Author.IconURL
		}
		if len(authorMap) > 0 {
			embedMap["author"] = authorMap
		}
	}

	// 添加页脚信息
	if embed.Footer != nil {
		footerMap := make(map[string]interface{})
		if embed.Footer.Text != "" {
			footerMap["text"] = embed.Footer.Text
		}
		if embed.Footer.IconURL != "" {
			footerMap["icon_url"] = embed.Footer.IconURL
		}
		if len(footerMap) > 0 {
			embedMap["footer"] = footerMap
		}
	}

	// 添加时间戳
	if embed.Timestamp != "" {
		embedMap["timestamp"] = embed.Timestamp
	}

	// 添加字段信息
	if embed.Fields != nil {
		fields := make([]map[string]interface{}, len(embed.Fields))
		for j, field := range embed.Fields {
			fields[j] = map[string]interface{}{
				"name":   field.Name,
				"value":  field.Value,
				"inline": field.Inline,
			}
		}
		embedMap["fields"] = fields
	}

	return embedMap
}

// isStandardField 检查字段是否为标准ProductItem字段
func (fpe *ProductExtractor) isStandardField(fieldName string) bool {
	standardFields := []string{
		"Title", "URL", "Description", "Color", "ProductID", "Price",
		"ImageURL", "ThumbnailURL", "FooterText", "FooterIconURL",
		"AuthorName", "AuthorURL", "AuthorIconURL", "Timestamp",
	}

	for _, field := range standardFields {
		if fieldName == field {
			return true
		}
	}
	return false
}
